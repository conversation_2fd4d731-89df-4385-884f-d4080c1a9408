export default class Plugin {
    apply() {
        return [{
            event: 'appFramework.bootstrap.before',
            functional: this.bootstrapBefore.bind(this)
        },{
            event: 'appFramework.render.before',
            functional: this.renderBefore.bind(this)
        }]
    }
    bootstrapBefore(plugin, { options }) {
        console.log('dht app appFramework.bootstrap.before call')
        console.log(options.appId);
        console.log(options.component);
        
        return Promise.resolve();
    }
    renderBefore(plugin, { options }) {
        console.log('dht app appFramework.render.before call')
        console.log(options.appId);
        console.log(options.component);
        
        return Promise.resolve();
    }
}
 