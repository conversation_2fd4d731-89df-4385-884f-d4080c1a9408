import _Md from "./Md.vue";
const Md = Vue.extend(_Md);
export default class Plugin {
  constructor(plugin, param) {
    this.widgets = {};
    // 是否使用自定义的md组件
    this.isCustomMd = false;
  }
  
  
  apply() {
    // return;
    // 仅在下游订货通中生效
    if(!window.$dht){
      return ;
    }

    // 仅在购物车页面中生效, 包含购物车和从购物车结算进入的新建页面
    // 标准订单列表中的新建页面中不生效, 因为无法添加订单产品从对象
    // if(!location.hash.includes('dht-api-ShoppingCartObj')){
    //   return ;
    // }

    return [
      {
        event: "form.render.before",
        functional: this.formRenderBefore.bind(this)
      },
      {
        event: "form.render.after",
        functional: this.formRenderAfter.bind(this)
      },
      {
        event: "md.render.before",
        functional: this.mdRenderBefore.bind(this)
      },
      {
        event: "pricePolicy.render.before",
        functional: this.pricePolicyRenderBefore.bind(this)
      },
    ];
  }
  // 当前是否处于购物车页面中
  isShoppingCard(plugin, param){
    let plugins = plugin.api.getPlugins();
    let rst = plugins.find(item => item.pluginApiName === 'dht_shoppingcart');
    return !!rst;
  }
  // 表单渲染之前
  formRenderBefore(param, plugin) { 
    const context = param;
    // const formType = param.formType;
    // this.formType = param.formType;
    this.isCustomMd = this.isShoppingCard(plugin, param);
    // 标准订单列表中的新建页面中不生效, 因为无法添加订单产品从对象
    // this.isCustomMd = isShoppingCard ? true: formType != 'add';
      
    console.log('formRenderBefore isCustomMd', this.isCustomMd);
    return Promise.resolve({});
  }

  initMd(plugin, param, $wrapper, opts){
    let me = this;

    const formType = param.formType;
    // console.log('formType', formType);
    const config = {
      pageType: formType  //card, add, edit, shoppingCard, detail
    };

    if (me.isShoppingCard(plugin, param)) {
      config.pageType = 'shoppingCard';
    } 
    // 详情页 config.pageType = 'detail'    

    let mdApiName = param.objApiName;
    let mdDescribeFields = param.dataGetter.getDescribe(mdApiName)?.fields;
    let recordType = param.recordType || 'default__c';
    // let basicData = param.getRowBasicData(mdApiName, recordType);
    // let masterData = param.dataGetter.getMasterData();
    let mdData = param.dataGetter.getDetail(mdApiName);
    Object.freeze(plugin);
    Object.freeze(param);

    // 与详情页复用相同的组件
    const onCheckChange = opts.onCheckChange;
    const MdCom = this.MdCom = new Md({
        propsData:{
            mdApiName,
            recordType,
            mdData,
            mdDescribeFields,
            plugin,
            param,
            config,
            onCheckChange
        }
    });

    MdCom.$mount();
    $wrapper.append(MdCom.$el);      
      
    // 如果 pageType为 cart(从购物车进入的新建) 或 edit(详情中编辑), mount之后就可以渲染表格, 
    // 不用等UI事件赋值分组信息
    // if(['cart', 'edit'].includes(config.pageType)) {
    //   MdCom.formRenderAfter(plugin, param);
    // }
   
    return {
      // rowIds为空, 表示全选或全不选
      checkDetailsRow:(rowIds, isChecked) => {
        console.log('checkDetailsRow', rowIds, isChecked);
        this.MdCom.checkDetailsRow(rowIds, isChecked);
      },
      // 获取选中数据, 返回全部的mdData
      getCheckedDatas:() => {        
        console.log('getCheckedDatas', this.MdCom);
        if(this.MdCom && this.MdCom.getCheckedDatas){ 
          return this.MdCom.getCheckedDatas();
        }
        return [];
      },
      // 所有数据, 提交时调用
      getValue: () => {  
        return this.MdCom.getMdData();
      },      
      validate:() => {
        return this.MdCom.validate(); // false 表示验证不通过, 阻断
      },
      // 有任何数据更新, 触发重新计算汇总信息
      update: (res) => { //res: {add: [{...}], update: {rowId: {....}, del: [rowId, rowId...], insert:[{insertRowId: 'xxx', datas: [{.....}]}]}}
        return this.MdCom.update(res);
      }
    }
  }
  
  /* 
    显示促销以及赠品详情  
  showPolicyDetail(row){
    console.log('do  showPolicyDetail', row);
    let ps = this.plugin.api.pluginServiceFactory({
          pluginApiName: 'customer_showPolicyDetail'
    });
    this.plugin.run('pricePolicy.showPolicyDetail', {
        type:"detail",
        dataId: row.prod_pkg_key,
        rowData: row
    });
    ps.end();
  }*/

  mdRenderBefore(param, plugin) {
    console.log('mdRenderBefore', plugin, param);    
    this.plugin = plugin; 
    this.param = param;

    // 仅 购物车页面的 从对象表格需要走客开,其余均不走客开
    if(!this.isCustomMd){
      return;
    }
    return {
      layoutSlot: this.initMd.bind(this, plugin, param),      
    }
  }  

  formRenderAfter(param, plugin) {
    if(this.isCustomMd){
      // console.log('custom formRenderAfter');      
      const res = this.MdCom.formRenderAfter(plugin, param);            
			plugin.run('md.custable.renderComplete');
      return res;
    }
    // dataUp
  }

  // sfa 提供,禁止整单促销显示
  pricePolicyRenderBefore(param, plugin) {
    // console.log('pricePolicyRenderBefore', plugin, param);
    if(this.isCustomMd){
      return {
        disableMasterPolicy:true   
      }
    }
  }
}
