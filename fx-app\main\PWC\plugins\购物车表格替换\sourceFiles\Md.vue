<template>
	<div v-if="ready" class="product-layout" :style="{height: tableHeight ? tableHeight + 'px' : 'auto' }"
		:class="[config.pageType === 'cart' && 'product-layout-cart',isDetail && 'product-layout-detail']">		
		<div class="table-module">
			<!-- 遍历dealers 渲染经销商卡片 -->
			<div v-for="[dealerKey, dealer] in Array.from(dealers.entries())" :key="dealerKey"
				class="table-module-list">
        <!--250114隐藏xuyupeng-->
				<!--<div v-if="isDetail" class="table-header-erp">ERP订单号：{{
					dealersSums[dealerKey].erp_order_number__c || '--' }}</div>-->
				<!-- 遍历brandAndDealer 渲染品牌卡片 -->
				<div v-for="[brandKey, brand] in Array.from(dealer.entries())" :key="brandKey"
					class="table-module-wrap">
					<div class="table-header">
						<span class="table-header-dealers">{{ brandDealerSums[dealerKey + brandKey].name || '--'
							}}</span>
						<span class="table-header-dealers">{{ dealersSums[dealerKey].name || '--' }}</span>
						<span class="table-header-contract">总合同目标 {{ roundNum(dealersSums[dealerKey].targetAmount) || 0 }} / {{
							roundNum(dealersSums[dealerKey].target_contract_amount) || '--' }} 元</span>
						<!-- v-if="dealersSums[dealerKey].contract_id" -->
						<a v-if="!isDetail" @click="showContractDetail(dealerKey)">查看明细</a>
					</div>
					<productTable :brand="brand" :dealer="dealer" :ref="'productTable-' + dealerKey + brandKey" :showSums="showSums"
						:showRemark="showRemark" :showNumber="showNumber" :isDetail="isDetail"
						:isShoppingCard="isShoppingCard" :productDescribe="productDescribe" :dealerKey="dealerKey"
						:roundNum="roundNum"
            :roundNum4="roundNum4"
						@delItem="delItem" @collectItem="collectItem" @showRowDetail="showRowDetail" @showPolicyDetail="showPolicyDetail" @handleNumChange="handleNumChange" @check="checkHandle">
					</productTable>
				</div>
				<div class="order-num order-num-custom" v-if="showSums">
					<div class="order-num-message">
						<div v-if="showRemark || isDetail || isEdit" class="remark-wrap">
							<span>留言备注</span><span v-if="isDetail">：{{ dealersSums[dealerKey].remark }}</span>
							<fx-input type="textarea" placeholder="请输入" v-model="dealersSums[dealerKey].remark" v-if="showRemark"
								:autosize="{ minRows: 1, maxRows: 4 }" maxlength="2000" show-word-limit size="small"
								@change="handleRemarkChange(dealerKey)">
							</fx-input>
						</div>
					</div>
					<div class="order-num-wrap">
						<div v-show="dealersSums[dealerKey].isShowDetail">
							<div class="order-num-item">
								<span class="label">核算价商品总金额:</span>
								<span class="num">{{ roundNum(dealersSums[dealerKey].aggregate.goods_num) }}元</span>
							</div>
							<!-- <div class="order-num-item">
								<span class="label">运费:</span>
								<span class="num">减{{ dealersSums[dealerKey].aggregate.ship }}元</span>
							</div> -->
							<div class="order-num-item">
								<span class="label">优惠金额:</span>
								<span class="num">{{ roundNum(dealersSums[dealerKey].aggregate.discount) }}元</span>
							</div>
							<!-- <div class="order-num-item">
								<span class="label">使用返利金额:</span>
								<span class="num">{{ roundNum(dealersSums[dealerKey].aggregate.rebate) }}元</span>
							</div> -->
							<div class="order-num-item">
								<span class="label">业绩考核金额:</span>
								<span class="num">{{ roundNum(dealersSums[dealerKey].aggregate.achievement) }}</span>
							</div>
						</div>
						<div class="order-num-item">
							<span class="label">应付总金额(含税):</span>
							<span class="num sum">{{ roundNum(dealersSums[dealerKey].aggregate.order_num) }}<span class="unit">元</span></span>
							<span v-show="dealersSums[dealerKey].isShowDetail" class="fx-icon-arrow-up icon"
								@click="dealersSums[dealerKey].isShowDetail = false"></span>
							<span v-show="!dealersSums[dealerKey].isShowDetail" class="fx-icon-arrow-down icon"
								@click="dealersSums[dealerKey].isShowDetail = true"></span>
						</div>

					</div>
				</div>
			</div>
		</div>		
	</div>
</template>

<script>
import productTable from "./product-table.vue";
export default {
	name: "cusmd",
	props: {
		mdApiName: {            // 从对象apiname
			type: String,
			default: ''
		},
		recordType: {            // 业务类型
			type: String,
			default: ''
		},
		config: {               // 配置
			type: Object,
			default: () => ({})
		},
		masterData: {            // 主对象主数据 销售订单
			type: Object,
			default: () => ({})
		},
		context: {               // 插件上下文
			type: Object,
			default: () => ({})
		},
		mdData: {               // 从对象数据 销售订单产品
			type: Array,
			default: () => []
		},
		mdDescribeFields: {             // 从对象描述
			type: Object,
			default: () => ({})
		},
		packageId: {            // 产品包产品id
			type: String,
			default: ''
		},
		plugin: {               // 插件
			type: Object,
			default: () => ({})
		},
		param: {            // 插件钩子
			type: Object,
			default: () => ({})
		},
		basicData: {            // 从对象公共数据
			type: Object,
			default: () => ({})
		},		
		onCheckChange: {
			type: Function,
			default: () => {}
		}	

	},
	components: {
		productTable,
		ObjectDetailRelatedlist: FxUI.component.get('ObjectDetailRelatedlist')
	},
	watch: {
		// mdData: {
		//   handler(val) {
		//     // console.log(newVal);
		//   },
		//   deep: true,
		//   immediate: true,
		// },
	},
	computed: {
		// 购物车页面, 显示删除按钮, 添加关注等按钮
		isShoppingCard() {
			return this.config.pageType === 'shoppingCard';
		},
		// 编辑活再来一单页面
		isEdit() {
			return this.config.pageType === 'clone' || this.config.pageType === 'edit';
		},
		// 金额汇总信息, 非购物车页均显示
		showSums() {
			return !this.isShoppingCard;
		},
		// 新建/编辑页  显示备注
		showRemark() {
			// console.log('showRemark', this.config.pageType);
			// cart 表示从购物车进入的新建页			
			return ['add', 'edit', 'cart'].includes(this.config.pageType);
		},
		isDetail() {
			return this.config.pageType === 'detail';
		},
		// 数量允许累加, 仅详情页不显示
		showNumber() {
			return !this.isDetail;
		}
	},
	data() {
		return {
			dealers: new Map(),  // 遍历的分组信息, 帐套id为key
			dealersSums: {}, // 目标合同信息, 经销商为维度汇总的金额信息  帐套id为key, 
			brandDealerSums: {}, // 品牌+进销售 为维度汇总的金额信息 帐套id+品牌id 为key,
			productDescribe: null,
			ready: false,
			checked: false,
			contractDetailOpts: {},
			checkedRowIdsMap: {}, // 已勾选的rowIds合集
			tableHeight: 0, // 表格高度
			initMdData: null, // 保存初始化过程中 更新的从对象数据
		}
	},
	methods: {
		// notify: 是否通知上层, 初始化时为false, 来自购物车的全选时为true, UI界面主动勾选为true
		checkHandle(rowIds, isChecked, notify){
			const idsMap = this.checkedRowIdsMap;
			rowIds.map(rowId => {
				if(isChecked){
					idsMap[rowId] = true;
				}else{
					delete idsMap[rowId];
				}
			});
			this.checkedRowIdsMap = idsMap;
			// console.log('checkedRowIdsMap', this.checkedRowIdsMap);
			if(notify) {
				this.onCheckChange && this.onCheckChange(rowIds, isChecked);
			}
			// this.$emit('checkRow', rowIds, isChecked);
		},
		// 获取所有选中的row数据
		getCheckedDatas() {
			const mdData = this.getMdData();
			let rst = mdData.filter(a => this.checkedRowIdsMap[a.rowId]);
			// console.log('getCheckedDatas', rst);
			return rst;
		},
		// 获取缓存中的MdData数据, 包含选中状态, 数量等信息, 以及被删除的信息
		getMdData() {
			if (!this.ready) {
				if(!this.initMdData) {
					this.initMdData = this.mdData;
				}
				return this.initMdData;
			}

			const dealers = this.dealers;
			let mdData = [],remark = '';
			dealers.forEach((brandAndDealer, dealerId) => {
				// console.log('brandAndDealer', brandAndDealer, dealerId);				
				brandAndDealer.forEach((item, index) => {
					// console.log('item', item, index);					
					mdData = mdData.concat(item);
				});
			});
			Object.entries(this.dealersSums).forEach(([key, value]) => {
				if (!value.name && !value.remark)return;
				if (!remark) {
					remark =  value.name + ':' + value.remark;
				} else {
					remark += '|' + value.name + ':' + value.remark;
				}
			});
			this.param.dataUpdater.updateMaster({
				remark: remark
			});
			this.param.end()
			return mdData;
		},	
		// 验证数据, 禁止全部删除
		validate(){
			const mdData = this.getMdData();
			if(mdData.length <= 0){
				this.$message.warning('请至少保留一个产品');
				return true;
			}
			// 目前需要返回 false 才能通过
			
			Object.values(this.dealersSums).forEach((item) => {
				if (item.remark && item.remark.indexOf('|') > -1) {
					this.$message.warning('留言备注中不可以有"|"字符');
					return true;
				}
			});
			return false;
		},
		// ui事件返回有任何数据更新, 触发表格内的数据同步更新
		//res: {add: [{...}], update: {rowId: {....}}, del: [rowId, rowId...], insert:[{insertRow: {...}, isBefore, datas: [{.....}]}, mdSort: [rowId, rowId...]}
		// 参考 crm2\modules\action\field\components\md20\layout_v2\table.js中updateHandle的实现, 有问题咨询王剑
		update(res) {
			// 如果表格未渲染完成, 则不执行更新操作
			console.log('Md.vue ui trigger update', res);			
			const {add, update, del, insert, mdSort} = res;
			let mdData = this.getMdData();			
			// 删除指定行
			del && (mdData = this.deleteByRowIds(del, mdData));
			// 新增指定行
			add && (mdData = this.addByRowIds(add, mdData));
			// 插入指定行
			insert && (mdData = this.insertByRowIds(res, mdData));
			// 更新指定行
			update && (mdData = this.updateByRowIds(update, mdData));
			// 排序 避免出现 赠品跑到了订单产品的前面了
			mdSort && (mdData = this.mdSortByRowIds(mdSort, mdData));

			// 需要保存 页面加载ui事件更新的数据, 否则线上ui事件提前返回时, 账套信息等未更新
			if(!this.ready){
				this.initMdData = mdData;
				return;
			}

			// 重新分组
			this.formatAndGroupByDealer(mdData);
			// 触发所有表格重新绘制
			this.dealers = new Map(this.dealers);

			this.$nextTick(() => {
				// 有删除时, 同步选中状态		
				this.updateCheckedRowIdsMap(res, mdData);
				this.checkDetailsRow(Object.keys(this.checkedRowIdsMap), true);
			});
		},

		// ui事件触发时 新增指定行
		addByRowIds(add, mdData){
			// 当mdData中不存在add时才添加, 否则会重复增加相同rowId的数据
			const newAdd = add.filter(a => !mdData.find(b => b.rowId === a.rowId));
			mdData.push(...newAdd);
			return mdData;
		},
		// insert: [{insertRow: {...}, isBefore, datas: [{.....}]}]
		insertByRowIds(res, mdData){
			const {add, insert, del} = res;			
			// 遍历insert, 将datas中的数据插入到mdData的 insertRow的正确位置, isBefore:true, 表示插入到insertRow之前, 否则插入到insertRow之后
			if(!insert) {
				return mdData;
			}
			insert.forEach((item) => {
				const {insertRow, isBefore, datas} = item;
				const index = mdData.findIndex(a => a.rowId === insertRow.rowId);
				if (isBefore) {
					mdData.splice(index, 0, ...datas);
				} else {
					mdData.splice(index + 1, 0, ...datas);
				}
			});			
			return mdData;
		},
		// ui事件触发时 更新指定行
		// update: {rowId1: {....}, rowId2: {....}}
		updateByRowIds(update, mdData){
			const rowIds = Object.keys(update);
			rowIds.forEach(rowId => {
				const index = mdData.findIndex(a => a.rowId === rowId);
				if(index >= 0){
					mdData[index] = Object.assign(mdData[index], update[rowId]);
				}
			});
			return mdData;
		},
		// ui事件触发时, 删除指定行
		deleteByRowIds(rowIds, mdData){
			// 遍历rowIds, 从mdData中删除指定行
			rowIds.forEach(rowId => {
				const index = mdData.findIndex(a => a.rowId === rowId);
				if(index >= 0){
					mdData.splice(index, 1);
				}
			});			
			return mdData;	
		},
		// 确保 赠品 在订单产品之后, 也即mdData中的数据顺序 按照mdSort顺序显示
		// mdSort: [rowId1, rowId2, ...]
		mdSortByRowIds(mdSort, mdData){
			// console.log('mdSortByRowIds', mdSort, mdData);
			// 创建一个Map来存储sortIds中每个id的索引
			const idIndexMap = new Map(mdSort.map((rowId, index) => [rowId, index]));

			// 将mdData分为两部分：需要排序的部分和不需要排序的部分
			const toSort = [];
			const others = [];

			mdData.forEach(item => {
				if (idIndexMap.has(item.rowId)) {
					toSort.push(item);
				} else {
					others.push(item);
				}
			});

			// 对需要排序的部分按照sortIds的顺序进行排序
			toSort.sort((a, b) => idIndexMap.get(a.rowId) - idIndexMap.get(b.rowId));

			// 合并排序后的部分和不需要排序的部分
			const result = [];
			let sortIndex = 0;
			let otherIndex = 0;

			mdData.forEach(item => {
				if (idIndexMap.has(item.rowId)) {
					result.push(toSort[sortIndex++]);
				} else {
					result.push(others[otherIndex++]);
				}
			});
			return result;
		},
		// 有数据删除时 更新选中rowIdsMap
		updateCheckedRowIdsMap(res, mdData) {
			const { add = [], insert = [], del = [] } = res;
			const checkedRowIdsMap = this.checkedRowIdsMap;

			// 从 add, insert 中超出被重新加回来的rowIds
			const addIds = add.map(a => a.rowId);
			const insertIds = insert.flatMap(a => a.datas.map(b => b.rowId));
			const allIds = [...addIds, ...insertIds];

			const mdMaps = {};
			// 详情页没有rowId, 需要使用 prod_pkg_key来代替, 主要是赠品通过其关联父产品
			const key = this.isDetail ? 'prod_pkg_key' : 'rowId';

			mdData.forEach(item => {
				mdMaps[item[key]] = item;
			});

			// 如果赠品对应的父产品被选中, 则赠品也要被自动选中, 其rowId也要添加到checkedRowIdsMap中
			allIds.forEach(rowId => {
				const rowItem = mdMaps[rowId];
				if (rowItem && rowItem.is_giveaway == '1' && checkedRowIdsMap[rowItem.parent_gift_key]) {
					checkedRowIdsMap[rowItem.rowId] = true;
				}
			});

			// 对比add, del, insert, 找出实际被删除的数据, 从选中rowIdsMap中删除
			const delIds = del.filter(a => !allIds.includes(a));
			delIds.forEach(rowId => {
				delete checkedRowIdsMap[rowId];
			});

			// 更新选中rowIdsMap
			this.checkedRowIdsMap = checkedRowIdsMap;
		},
		// 初始化时, 以及全选时, 选中指定的表格行
		checkDetailsRow(rowIds, isChecked){
			console.log('checkDetailsRow', rowIds, isChecked);
			
			if(!this.ready) {
				// 未初始就被提前调用场景, 仅记录选中的rowIds
				this.checkHandle(rowIds, isChecked, false);
				return;
			}
			
			let allRowIds = [];
			this.dealers.forEach((brandAndDealer, dealerKey) => {
				brandAndDealer.forEach((items, brandKey) => {
					const refName = 'productTable-' + dealerKey + brandKey;
					this.$refs[refName].forEach(table => {
						let rowsToToggle = [];

						// 如果 rowIds 为空，表示全选或取消全选
						if (!rowIds) {
								rowsToToggle = items;
								allRowIds = allRowIds.concat(items.map(a => a.rowId));
						} else {
								// 否则，选中或取消选中指定的行
								rowsToToggle = items.filter(row => rowIds.includes(row.rowId));
						}
						// 执行一次 toggleRowSelection
						rowsToToggle.forEach(row => {
								table.toggleRowSelection(row, isChecked);
						});
					});
				});
			});

			this.checkHandle(allRowIds.length ? allRowIds : rowIds, isChecked, true);			
		},
		// 渲染后, 选中指定的表格行
		mdRenderAfter(plugin, param) {
			// console.log('mdRenderAfter', plugin, param);
		},
		// 初始化时, 设置默认选中行
		setDefaultCheckRow(plugin, param){
			let mdData = param.dataGetter.getDetail(this.mdApiName);
			let rowIds = [];
			mdData.forEach(a => {
				if(a.is_settled) {
					rowIds.push(a.rowId);
				}
			});
			this.checkDetailsRow(rowIds, true);
		},
		formRenderAfter(plugin, param) {
			// console.log('formRenderAfter', plugin, param);

			let mdData = param.dataGetter.getDetail(this.mdApiName);
			let masterData = param.dataGetter.getMasterData();
			this.formatAndGroupByDealer(mdData);
			const customerId = masterData.account_id;
			this.getDealerContract(customerId);
			// 仅编辑页需要回填备注
			if(this.config.pageType === 'edit' || this.config.pageType === 'clone') {
				this.setRemark(masterData)
			}
			this.ready = true;
			// this.$nextTick(() => {
			// 	this.setDefaultCheckRow(plugin, param);
			// });
			return Promise.resolve();
		},
		formatAndGroupByDealer(mdData) {
			mdData = this.fixMdData(mdData);
			this.groupByDealer(mdData);
			this.isShoppingCard && this.initHeight()
			if (this.showRemark && this.$el.closest) {
				this.$el.closest('.crm-action-nfield .box-more .f-n-group').style.marginTop = '24px'
			}
		},
		/* 获取当前登录商户与洁柔签订的 经销商合同, 
		 * 返回的数据merge 形式存在 this.dealersSums中 
		*/
		getDealerContract(customerId) {
			// console.log('getDealerContract');

			// 入参 帐套ids, 当前商户id ( APL中根据cookie获取)
			const dealerIds = Array.from(this.dealers.keys()).filter(id => !!id);
			if (dealerIds.length <= 0) {
				return Promise.resolve([]);
			}		

			// let dealerIds = ['66e148ba4d45c200011c537f'];
			// let customerId = '66eb81c82b268400070d74af';
			const parameters = [{
				type: 'list',
				name: 'dealerIds',
				value: dealerIds
			}, {
				type: 'string',
				name: 'customerId',
				value: customerId
			}];
			/* 
			var parameters = [{"type":"list","name":"dealerIds","value":["66e148824d45c200011c503a","66e148824d45c200011c503e"]},{"type":"string","name":"customerId","value":"66e13b74b53eab0001c9d59f"}]
			 */
			FxUI.userDefine.call_controller('get_dealer_contracts__c', parameters).then((res) => {
				if (res.Result.StatusCode == 0 && res.Value.functionResult) {
					// console.log(res.Value);
					const list = res.Value.functionResult.dataList;
					// 浅拷贝, 确保触发ui响应 
					const dealersSums =  Object.assign({}, this.dealersSums);
					list.forEach(item => {
						// field_ye82P__c 年度目标, sales_orxiaoshganization__c
						const { sales_organization__c, field_ye82P__c, _id } = item;
						dealersSums[sales_organization__c].target_contract_amount = field_ye82P__c;
						dealersSums[sales_organization__c].contract_id = _id;
					});
					// 触发合同信息异步更新
					this.dealersSums = dealersSums;
				}
			}).catch(err => {
				console.log(err);
			})
		},

		// 显示经销商合同明细
		showContractDetail(dealerId) {
			const dealerInfo = this.dealersSums[dealerId];
			const detailOpts = {
				// 经销商合同
				apiName: 'object_fPYTG__c',
				// 合同id
				dataId: dealerInfo.contract_id,
				compInfo: {
					field_api_name: "master_slave_relationship__c",
					header: "经销商目标",
					ref_object_api_name: "dealer_target__c",
					related_list_name: "target_related_list_c41g2__c"
				}
			}
			this.contractDetailOpts = detailOpts;
			this.showContractDialog = true;
		},

		// 获取产品描述信息, 获取标签名称和颜色, 标签字段: commodity_label
		getProductDescribe() {
			if (this.productDescribe) {
				return Promise.resolve(this.productDescribe);
			}
			return FxUI.objectApi.fetch_describe('ProductObj').then((describe) => {
				// console.log(describe);
				this.productDescribe = describe;
			}).catch(err => {
				console.log(err);
			});
		},

		// 补充字段mdData中缺失的字段信息 如品牌, 经销商, 标签tag等
		fixMdData(mdData) {
			console.log('fixMdData', mdData);
			
			// 将mdData 转换为 mdMaps 格式
			const mdMaps = this.mdMaps || {};
			// 详情页没有rowId, 需要使用 prod_pkg_key来代替, 主要是赠品通过其关联父产品
			let key = this.isDetail ? 'prod_pkg_key' : 'rowId';

			mdData.forEach(item => {				
				mdMaps[item[key]] = item;
			});		
			this.mdMaps = mdMaps;
			// 赠品的 账套:account_suite__c, 品牌:brand_option__c 要取父产品的
			mdData.forEach(item => {
				const { parent_gift_key, is_giveaway } = item;
				if (is_giveaway) {
					const parentItem = mdMaps[parent_gift_key] || {};
					item.account_suite__c = parentItem.account_suite__c;
					item.account_suite__c__r = parentItem.account_suite__c__r;
					item.brand_option__c = parentItem.brand_option__c;
				}
			});
			return mdData;
		},
		// 按照品牌, 经销商分组
		groupByDealer(mdData) {
			// 按照品牌, 经销商分组 
			const dealers = new Map();

			// 浅拷贝, 避免分组后, 保存的备注信息丢失
			const dealersSums = Object.assign({}, this.dealersSums);
			const brandDealerSums = Object.assign({}, this.brandDealerSums);
			let quantity = 1;


			mdData.forEach(item => {
				// 发货日期: field_delivery_date__c 
				// 账套:account_suite__c, 品牌:brand_option__c 
				const { account_suite__c, brand_option__c } = item;
				if (!dealers.has(account_suite__c)) {
					dealers.set(account_suite__c, new Map());

					// 经销商名称信息
					dealersSums[account_suite__c] = Object.assign(
						// 默认值
					{
							remark: '',
							isShowDetail: false,
							erp_order_number__c: '',
					}, 
					// 历史值
					dealersSums[account_suite__c], 
					// 需要更新的 新值
					{
						account_suite__c,
						name: item.account_suite__c__r,
						targetAmount: 0,
						// 汇总计算 数据清零
						aggregate: {
								goods_num: 0,
								ship: 0,
								discount: 0,
								rebate: 0,
								achievement: 0,
								order_num: 0
						}
					});
				}
				const brandAndDealer = dealers.get(account_suite__c);
				if (!brandAndDealer.has(brand_option__c)) {
					brandAndDealer.set(brand_option__c, []);

					// 订单产品描述信息
					const brandField = this.mdDescribeFields?.brand_option__c;
					let brandVal = FxUI.objectApi.format_field_value(brandField, brand_option__c, item);
					brandDealerSums[account_suite__c + brand_option__c] = {
						brand_option__c,
						name: brandVal, // 品牌名称
					};
				}
				brandAndDealer.get(brand_option__c).push(item);
				dealersSums[account_suite__c].targetAmount += Number(item.field_wcqx6__c); // 目标金额
				dealersSums[account_suite__c].erp_order_number__c = item.erp_order_number__c; // erp编号
				if (!this.isShoppingCard) {
					// if(!item.is_giveaway) {
						quantity = Number(item.quantity) || 1;								
						dealersSums[account_suite__c].aggregate.goods_num += (Number(item.calculated_price__c) * quantity)
						dealersSums[account_suite__c].aggregate.order_num += (Number(item.product_price) * quantity)
						dealersSums[account_suite__c].aggregate.discount += Number(item.field_cb610__c)
						// 业绩考核金额
						dealersSums[account_suite__c].aggregate.achievement += Number(item.field_wcqx6__c)
					// }
				}
			});			
			
			// data中的 dealers 和 brandAndDealer 赋值
			this.dealers = dealers;
			this.dealersSums = dealersSums;
			this.brandDealerSums = brandDealerSums;

			// 显示分组结果
			// dealers.forEach((brandAndDealer, a) => {
			//   console.log(`a: ${a}`);
			//   let aCount = 0;
			//   brandAndDealer.forEach((items, b) => {
			//     console.log(`  b: ${b}`);
			//     console.log(`    数量: ${items.length}`);
			//     console.log(items);          
			//     aCount += 1;
			//   });
			//   console.log(`类型 "${a}" 中共有 ${aCount} 个 b 分组\n`);
			// });
		},
		setRemark(data){
			// console.log(data);
			if (data.remark) {
				let list = data.remark.split("|");
				list.forEach(item => {
					let arr = item.split(":");
					Object.entries(this.dealersSums).forEach(([key, value]) => {
						if (value.name == arr[0]) {
							let remark = '';
							for (let i = 1; i < arr.length; i++) {
								remark += arr[i];
                if (arr[i + 1]) {
									remark += ':'
								}
							}
							this.dealersSums[key].remark = remark;
						}
					})
				})
			}
		},
		init() {
			const mdData = this.fixMdData(this.mdData);
			this.groupByDealer(mdData);

			// console.log(this.param);
			// console.log(this.plugin);
		},
		async handleNumChange(val, row) {
			console.log('handleNumChange', val, row);

			const rowId = row.rowId;
			const fieldName = 'quantity';
			const changeData = {
				quantity: val,
			};			      

			this.param.runMDEditService({
				objApiName: this.mdApiName,
				recordType: this.recordType,
				fieldName,
				dataIndex: [rowId],
				fieldEditTask: () => {
					return {
						then: resolve => resolve({
							[rowId]: changeData
						})
					}
				},
				finallyCallback: (error) => {
					//todo not finish 如果 error , 数据回滚
					// !error && opts.isContinue && this.handleBatchEdit({}, objApiName, recordType);
				}
			});

		},
		// 根据rowId 获取mdData中的行数据
		findRowById(rowId){
			const row = this.mdData.find(item => item.rowId === rowId);
			return row;
		},
		//update 备注信息
		handleRemarkChange(key) {
			console.log(this.dealersSums[key].remark);
			this.validate()
		},
		// 获取缓存中的行数据, 保存是否选中, 数量等信息
		getCacheRow(row) {
			const rowId = row.rowId;
			const brandAndDealer = this.dealers.get(row.account_suite__c).get(row.brand_option__c);
			const item = brandAndDealer.find(item => item.rowId === rowId);
			return item;
		},
		delItem(row) {
			console.log('delItem', row);			
			const rowId = row.rowId;
			const brandAndDealer = this.dealers.get(row.account_suite__c).get(row.brand_option__c);
			const index = brandAndDealer.findIndex(item => item.rowId === rowId);
			brandAndDealer.splice(index, 1);

			// console.log('删除一行');
			let dealers = this.dealers;
			// 如果品牌+经销商分组为空, 则删除对应分组
			if(brandAndDealer.length <= 0){
				dealers.get(row.account_suite__c).delete(row.brand_option__c);
				delete this.brandDealerSums[row.account_suite__c + row.brand_option__c];
				// 触发分组信息更新, 必须是 new Map包裹才行
				this.$set(this, 'dealers',  new Map(dealers));
			}	

			// 正确写法
			this.plugin.api.proxyService('runMDDelService', {
				  objApiName: this.mdApiName,
			    recordType: this.recordType,
			    delDatas: [row]
			});
		},
		// 收藏赠品
		collectItem(row) {
			console.log('do  collectItem', row);
			const util = this.plugin.api;
			util.import('vcrm/sdk').then((SDK) => {
				SDK.widgetService.getService('collectionService').then((res) => {
					const collectionService = res.collectionService;
					let params = {
						apiName: 'ProductObj',
						objId: row.product_id, // 产品id
					}						
					collectionService.addToCollectionList(params).then(res => {
						this.$message({
							message: !this.isCollection ? $t('收藏成功') : $t('已取消收藏'),
							type: 'success',
							center: true,
							duration: 1000
						});
					})
				})
			});			
		},
		// 显示对应的产品详情
		showRowDetail(row) {
			if (row.product_id == "" || row.product_id == null) {
			} else {
				FxUI.objectUIAction.viewObject("ProductObj", row.product_id);
			}
		},
		showPolicyDetail(row) {
			console.log('do  showPolicyDetail', row);
			let rowData = row;
			let type = 'detail';
			// 如果行数据是赠品, 则取父产品
			if(row.is_giveaway) {
				if (row.parent_gift_key === 'master') {	
					rowData = null;
					type = 'master';
				} else {
					rowData = this.mdMaps[row.parent_gift_key] || null;
				}
			}

			// 如果行数据为空, 则当成整单促处理
			if(!rowData) {
				type = 'master';
			}

			let ps = this.plugin.api.pluginServiceFactory({
				pluginApiName: 'customer_showPolicyDetail'
			});
			this.plugin.run('pricePolicy.showPolicyDetail', {
				type,
				dataId: rowData && rowData.prod_pkg_key,
				rowData: rowData || {},
			});
			ps.end();
			this.$emit('showPolicyDetail', row);
		},
		getShoppingCard(plugin, param) {
			let plugins = plugin.api.getPlugins();
			let rst = plugins.find(item => item.pluginApiName === 'dht_shoppingcart');
			return rst;
		},
		// 对于给定的字符串形式的数字, 如"30.00000000", 用四舍五入 保留两位小数
		// 如果结果为'NaN', 则返回 '--'
		roundNum(num) {
			// 解决浮点运算精度丢失问题
			let str = (Math.round(num + "e+2") + "e-2");			
			let rst = Number(str).toFixed(2);	
			return rst === 'NaN' ? '--' : rst;
		},
    roundNum4(num) {
			let str = (Math.round(num + "e+2") + "e-2");
			let rst = Number(str).toFixed(4);	
			return rst === 'NaN' ? '--' : rst;
		},
		initHeight() {
			if (this.tableHeight)return;
			this.$nextTick(() => {
				const containerHeight = this.$el.parentElement.getBoundingClientRect && this.$el.parentElement.getBoundingClientRect();
				if (containerHeight) {
					this.tableHeight = window.innerHeight-containerHeight.top - 60
				}
			})
		}
	},
	created() {
		this.getProductDescribe();
	},
	mounted() {		
	},
}
</script>
<style lang="less">
.product-layout {
	.order-num {
		padding: 16px;
		display: flex;
		justify-content: space-between;
		color: #545861;
		font-size: 14px;
		border-top: 1px solid #DEE1E8;
		.order-num-message {
			width: 600px;
		}

		.order-num-wrap {
			// padding-top: 16px;

			.order-num-item {
				position: relative;
				text-align: end;

				span {
					display: inline-block;
				}

				.label {
					min-width: 130px;
					margin-right: 4px;
				}

				.num {
					width: 110px;
					margin-right: 16px;
				}

				.sum {
					font-size: 16px;
					font-style: normal;
					font-weight: 700;
					color: #FF522A;

					.unit {
						font-size: 12px;
						font-weight: 400;
					}
				}

				.icon {
					position: absolute;
					right: 0px;
					top: 8px;
					font-size: 12px;

					&::before {
						color: #C1C5CE;
					}
				}
			}

		}
	}
}
.product-layout-cart {
	.order-num {
		padding: 16px 0;
	}
	.order-num-custom {
		padding: 16px;
	}
}
</style>
<style lang="less" scoped>
.product-layout {
	background: #F6F6F6;
	padding: 8px;

	.header-module {
		display: flex;
		justify-content: space-between;
		padding: 4px 0 2px 24px;
		align-items: baseline;
		background: #fff;
		color: #545861;
		border-radius: 4px;

		.header-item-address {
			box-sizing: border-box;
			height: 36px;
			line-height: 36px;
			padding: 0 14px;
			font-size: 14px;
		}
	}

	.table-module {
		height: calc(~"100% - 2px");
		overflow-y: scroll;
		// margin-top: 8px;

		.table-module-list {
			background: #fff;
			padding: 0 12px;
			margin-bottom: 8px;
			border-radius: 4px;
			.table-header-erp {
				color: #181C25;
				font-size: 14px;
				font-weight: 700;
				line-height: 20px; 
				padding: 0 16px;
				margin-bottom: 4px;
			}
			.table-module-wrap {
				.table-header {
					background: #fff;
					height: 46px;
					line-height: 46px;
					padding: 0 16px;
					box-sizing: border-box;
					font-size: 12px;

					&-dealers {
						font-weight: 700;
						font-size: 14px;
						margin-right: 4px;
					}

					&-contract {
						padding: 0 8px;
						border-left: 1px solid #DEE1E6;
					}
				}
			}

		}
	}
}

.product-layout-cart {
	background: #fff;
	padding: 0;

	.table-module {
		height: 100%;

		.table-module-list {
			padding: 0 !important;
			border-radius: 8px;
			border: 1px solid #DEE1E8;

			.table-module-wrap {
				margin-top: 8px;
				padding: 0 16px;

				.table-header {
					border-radius: 8px;
				}
			}
		}
	}
}
</style>