Map data = context.data as Map;
log.info(data["record_type"])
String record_type = data["record_type"];
QueryTemplate template = null

switch(record_type) {
  //是经销商的时候
  case "dealer__c":
    template = QueryTemplate.AND(
      ["name":null]
    )
    break;
  //是二批商的时候
  case "secondary_dealer__c":
    template = QueryTemplate.AND(
      ["owner":Operator.LIKE("1000")],
      ["record_type":Operator.EQ( "dealer__c" )]
    )
  break;
  //是门店的时候
  case "default__c":
    template = QueryTemplate.AND(
      ["owner":Operator.LIKE("1000")],
      ["record_type":Operator.IN(["dealer__c","secondary_dealer__c"])]
    )
  break;
}

return template;