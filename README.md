# dht_spalding
## 背景
斯伯丁体育用品（中国）有限公司 的沙盒 客开代码

### 账号 
上游账号:   820495_sandbox / 17621612679 / LYZ966589 
下游账号:   17621612679  / LYZ966589 , 上游ea: 820495_sandbox,  名称: 斯伯丁体育用品（中国）有限公司

## 主要客开功能
区分期货和现货, 通过销售订单的业务类型+可售范围过滤产品显范围实现, 每次进入时选取期货和现货业务类型, 有全量促销产品, 既可以按照期货卖,也可以按照现货卖

### 添加期货购物车时按照日期+产品添加多条购物车数据
实现思路, 在互联应用初始化的钩子函数中,向订货通中添加插件,如
```js
const diffFunc = (newData: CartProduct, storeCartList: CartProduct[]) => {
    const storeDatas = storeCartList.filter((storeCartItem: CartProduct) => {
      return storeCartItem.product_id === newData.product_id;
    });
    // 没有相同的sku
    if (!storeDatas.length) {
      return undefined;
    }

    return storeDatas.find((item) => isSameAttrProduct(newData, item));
  };
/**
 * 对比两个产品对象是不是同一个产品
 * @param source 源产品对象
 * @param target 目标产品对象
 * @returns boolean
 */
export function isSameAttrProduct(source: CartProduct, target: CartProduct): boolean {
  // 保险一点，再次校验是否是同一个产品
  if (source.product_id !== target.product_id) {
    return false;
  }
  // 这里给个空对象默认值，表示即使不传attribute_json或者传空对象，代表是非属性值产品
  const newAttributeJson = source.attribute_json || {};
  const oldAttributeJson = target.attribute_json || {};

  const newAttributeKeys = Object.keys(newAttributeJson);
  const oldAttributeKeys = Object.keys(oldAttributeJson);

  if (newAttributeKeys.length !== oldAttributeKeys.length) {
    return false;
  }

  return newAttributeKeys.every(
    (key) => newAttributeJson[key] === oldAttributeJson[key],
  );
}

$dht.usePlugin({
  module: 'cart',
  type: 'diffFn',
  pluginFn: diffFunc
})
```

### 判断当前用户选择的销售订单的业务类型
$dht.config.customer.record_type == "default__c"

首页点击后设置业务类型并刷新
```js
// 跳转到期货, 注意自行修改业务类型和tab Id
const recordApiName = "record_futures__c";
const tabId = 'dht_shopmall_specify_product_range-FSAID_11490c84_1752127069179'
// 设置业务类型
$dht.setMainRecordType({
  api_name: recordApiName,
})
.then(() => {
  // 刷新页面
  location.href=`/XV/Cross/Portal?fs_out_appid=FSAID_11490c84#/portal/depend/dht-api-ShopMall?tab=${tabId}`;
});
```

### 购物车按照交货日期分组
