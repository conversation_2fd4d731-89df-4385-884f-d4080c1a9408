<template>
    <div class="product-table-list">
        <fx-table :data="brand" 
            ref="productTable"
            style="width: 100%"
            :row-class-name="tableRowClassName"
             @select="selectHandle"
             @select-all="selectAllHandle"
        >
            <fx-table-column v-if="isShoppingCard" type="selection" align="center" width="64" label="" 
            :selectable="isRowSelectable"
            >               
            </fx-table-column>
            <fx-table-column prop="product_id__r" label="商品信息" min-width="414">
                <template slot-scope="scope">
                    <div class="product-r-wrap">
                        <div class="product-r-item" style="cursor: pointer;" @click.stop="showRowDetail(scope.row)">
                            <img class="product-r-img" :src="getProductPicture(scope.row)" alt="" width="95"
                                height="95" />
                            <div class="product-r-detail ">
                                <p class="product-r-label">{{ scope.row.product_id__r || '' }}</p>
                                <p class="product-r-unit">规格：{{ scope.row.field_product_specification__c || '' }}</p>
                                <span v-for="label in getLabelList(scope.row.label__c__v)" :key="label.value"
                                    :style="{ color: label.font_color }"> {{ label.label }}</span>
                            </div>
                        </div>
                        <template v-if="!isDetail" >
                            <div v-if="getActivePolicyInfo(scope.row.policyDetails)" class="product-r-policy">
                                <!--<span class="policy-name">{{  getActivePolicyInfo(scope.row.policyDetails).name }}</span>-->
                                <span class="policy-name">促销优惠</span>
                                <!-- 规则名称 显示: v-if="getActivePolicyInfo(scope.row.policyDetails).ruleNames" -->
                                <span v-if="0" class="policy-content">{{ getActivePolicyInfo(scope.row.policyDetails).ruleNames }}</span>
                                <!-- 切换活动 v-if="!isDetail"-->
                                <span v-if="0" class="policy-content"  style="cursor: pointer;" @click.stop="showPolicyDetail(scope.row)">切换活动<span class="fx-icon-arrow-right icon"></span></span>
                            </div>
                        </template>
                        <template v-if="isDetail && scope.row.price_policy_id" >
                            <div  class="product-r-policy">
                                <span class="policy-name">{{ scope.row.price_policy_id__r }}</span>
                                <!-- 规则名称 显示: v-if="0" -->
                                <span v-if="0" class="policy-content">{{ scope.row.price_policy_rule  }}</span>
                            </div>
                        </template>
                    </div>
                </template>
            </fx-table-column>
            <fx-table-column prop="calculated_price__c" align="center" label="核算价（元）" width="150">
                <template slot-scope="scope">
                    <span style="text-decoration: line-through;color: #000;">{{
                        roundNum(scope.row.calculated_price__c) }}</span>
                </template>
            </fx-table-column>
            <fx-table-column prop="product_price" align="center" label="折扣价（元）" width="150">
                <template slot-scope="scope">
                    <span style="color: #000;">{{ roundNum(scope.row.product_price) }}</span>
                </template>
            </fx-table-column>
            <fx-table-column prop="field_11K3P__c" align="center" label="散件价(仅供参考)" width="150">
                <template slot-scope="scope">
                    <span style="color: #000;">{{ roundNum(scope.row.field_11K3P__c) }}</span>
                </template>
            </fx-table-column>
            <fx-table-column prop="quantity" label="数量" width="150">
                <template slot-scope="scope">
                    <fx-input-number v-if="showNumber" size="mini" :key="scope.row._id + '_quantity'" :min="1"
                        :disabled="scope.row.is_giveaway" v-model="scope.row.quantity"
                        @change="handleNumChange(scope.row.quantity, scope.row)"></fx-input-number>
                    <span v-else style="color: #000;">x {{ scope.row.quantity }}</span>
                </template>
            </fx-table-column>
            <fx-table-column label="小计（元）" width="150">
                <template slot-scope="scope">
                    <div class="table-tb-num">
                        <span>{{ roundNum(scope.row.product_price * scope.row.quantity) }}</span>
                        <span class="unit">元</span>
                    </div>
                    <p style="color: #91959E;" v-if="!scope.row.is_giveaway && (scope.row.calculated_price__c - scope.row.product_price) * scope.row.quantity"
                        >共减{{ roundNum((scope.row.calculated_price__c - scope.row.product_price) * scope.row.quantity) }}元
                    </p>
                </template>
            </fx-table-column>
            <fx-table-column v-if="!isDetail" label="操作" align="center" width="81">
                <template slot-scope="scope">
                    <span v-if="!scope.row.is_giveaway" class="fx-icon-process-delete" style="font-size: 18px;cursor: pointer;"
                        @click="delItem(scope.row)"></span>
                    <!-- <span v-if="!scope.row.is_giveaway" style="font-size: 18px;cursor: pointer;"
                        @click="collectItem(scope.row)">收藏</span> -->

                    <span v-if="scope.row.is_giveaway" style="cursor: pointer;" @click="showPolicyDetail(scope.row)">换赠品<span class="fx-icon-arrow-right" style="font-size: 12px;"></span></span>
                </template>
            </fx-table-column>
        </fx-table>
        <div class="order-num" v-if="showSums && brandLength > 1">
            <div class="order-num-message">
            </div>
            <div class="order-num-wrap">
                <div v-show="isShowDetail">
                    <div class="order-num-item">
                        <span class="label">核算价商品总金额:</span>
                        <span class="num">{{ roundNum(aggregate.goods_num) }}元</span>
                    </div>
                    <!-- <div class="order-num-item">
                        <span class="label">运费:</span>
                        <span class="num">减{{ aggregate.ship }}元</span>
                    </div> -->
                    <div class="order-num-item">
                        <span class="label">优惠金额:</span>
                        <span class="num">{{ roundNum(aggregate.discount) }}元</span>
                    </div>
                    <!-- <div class="order-num-item">
                        <span class="label">使用返利金额:</span>
                        <span class="num">{{ roundNum(aggregate.rebate) }}元</span>
                    </div> -->
                    <div class="order-num-item">
                        <span class="label">业绩考核金额:</span>
                        <span class="num">{{ roundNum(aggregate.achievement) }}</span>
                    </div>
                </div>
                <div class="order-num-item">
                    <span class="label">应付总金额(含税):</span>
                    <span class="num sum">{{ roundNum(aggregate.order_num) }}<span class="unit">元</span></span>
                    <span v-show="isShowDetail" class="fx-icon-arrow-up icon" @click="isShowDetail = false"></span>
                    <span v-show="!isShowDetail" class="fx-icon-arrow-down icon" @click="isShowDetail = true"></span>
                </div>

            </div>
        </div>
    </div>
</template>

<script>
export default {
    props: {
        dealer: {
            type: Object,
            default: () => ({})
        },
        brand: {
            type: Array,
            default: () => ([])
        },
        productDescribe: { // 从对象描述
            type: Object,
            default: () => ({})
        },
        showSums: Boolean,
        showRemark: Boolean,
        showNumber: Boolean,
        isDetail: Boolean,
        dealerKey: String,
        isShoppingCard: Boolean,
        roundNum: Function,
        roundNum4: Function,
    },
    data() {
        return {
            selectedRows: [], //默认选中所有行
            isShowDetail: false,
            aggregate: {
                goods_num: 0,
                ship: 0,
                discount: 0,
                rebate: 0,
                achievement: 0,
                order_num: 0
            }
        }
    },
    watch: {
        // 监听 品牌数据变化, 重新计算汇总金额
        brand: {
            handler(newVal) {
                this.orderNum();
            },
            deep: true
        }
    },
    computed: {
       brandLength() {
        const len = Array.from(this.dealer.entries()).length;
        // console.log('brandLength', len);        
        return len;
       }
    },
    methods: {
        // 找到匹配的政策信息 isActive:true
        getActivePolicyInfo(policyDetails) {
            if (!policyDetails?.length) return null;
            
            const activePolicy = policyDetails.find(policy => policy.isActive);
            // 没有匹配上促销政策时,不显示, 返回null
            if(!activePolicy) return null;
            
            // 遍历activePolicy.rules, 返回 isActive的rule name
            const activeRule = activePolicy?.rules?.find(rule => rule.isActive);
            // 没有匹配促销政策中的 规则时,也整行不显示, 返回null
            if(!activeRule) return null;
                        
            return {
                name: activePolicy.name,
                ruleNames: activeRule?.name,
                policy: activePolicy
            };
        },
        // 赠品行, 背景置灰    
        tableRowClassName({ row }) {
            return row.is_giveaway ? 'row-giveaway-bg' : '';
        },
        // 单个表格中的 全选/取消全选selection:[], 通知 购物车插件更新
        selectAllHandle(selection) {
            // console.log('selectAllHandle', selection);
            let isChecked = false;
            const rowIds = this.brand.map(a => a.rowId);
            if(selection.length > 0) {
                isChecked = true;
            }
            this.$emit('check', rowIds, isChecked, true);
        },
        // 选中单个/取消选中, 通知 购物车插件更新
        selectHandle(selection, row) {
            // console.log('selectHandle', selection, row);
            let isChecked = false;
            const rowIds = [row.rowId];
            if(selection.findIndex(a => a.rowId === row.rowId) >= 0) {
                isChecked = true;
            }
            this.$emit('check', rowIds, isChecked, true);
        },
        toggleRowSelection(row, isChecked) {
            // console.log('toggleRowSelection', row, isChecked);
            // 赠品不触发 选中/取消选中事件
            // if(!this.isRowSelectable(row)) return;
            // 单条选中/取消选中
            this.$refs.productTable.toggleRowSelection(row, isChecked);
        },
        // 赠品不可选
        isRowSelectable(row, index) {
            return !row.is_giveaway;
            // return true;
        },
        // 计算汇总金额信息
        orderNum() {
            if (this.isShoppingCard) return
            let aggregate = {
                goods_num: 0, // 核算价总金额
                ship: 0, // 运费
                discount: 0, // 优惠金额
                // rebate: 0, // 使用返利金额
                achievement: 0, // 业绩考核金额
                order_num: 0 // 应付总金额(含税)
            }
            let quantity = 0;
            this.brand.forEach(item => {
                // item 为订单产品单条数据
                // if(!item.is_giveaway) { 
                    quantity = Number(item.quantity) || 1;
                    // 促销价 * 数量
                    aggregate.goods_num += (Number(item.calculated_price__c) * quantity)
                    // 核算价 * 数量
                    aggregate.order_num += (Number(item.product_price) * quantity)

                    // 优惠金额(field_cb610__c)累加 
                    aggregate.discount += Number(item.field_cb610__c)
                    
                    // 业绩考核金额(field_wcqx6__c)累加
                    aggregate.achievement += Number(item.field_wcqx6__c)
                // }
            })
            this.aggregate = aggregate;
        },
        delItem(row) {
            // console.log('delItem', row);
            this.$emit('delItem', row);
        },
        // 收藏赠品
        collectItem(row) {
            // console.log('collectItem', row);
            this.$emit('collectItem', row);
        },
        // 显示行详情
        showRowDetail(row) {
            this.$emit('showRowDetail', row);
        },
        showPolicyDetail(row) {
            // console.log('showPolicyDetail', row);
            this.$emit('showPolicyDetail', row);
        },
        handleNumChange(val, row) {
            this.orderNum()
            this.$emit('handleNumChange', val, row);
        },
        getProductPicture(product) {
            if (product.product_image && product.product_image.length > 0) {
                return product.product_image[0].signedUrl + '&size=100*0';
            } else {
                return 'http://a9.fspage.com/open/cdn/img/sail.default.png';
            }
        },
        getLabelList(data) {
            // let res = await FxUI.objectApi.fetch_data('ProductObj', data.product_id).then((item) => {
            //     console.log(item);
            //    return item
            // })
            // console.log(res);
            let options = this.productDescribe?.objectDescribe?.fields.commodity_label?.options || [];
            if (data && data.length > 0) {
                data = data.map(item => {
                    let obj = options.find(option => option.value === item);
                    return obj || {};
                })
                return data
            } else {
                return []
            }
        }
    },
    created() {
        // console.log('created', this.brand);
        // let rows = this.brand.map(a => a);
        // console.log('rows', rows);        
        // this.selectedRows = rows;
    },
    mounted() {
        // console.log(this.brand);
        // 详情页中 需要手动触发一次 计算汇总金额
        if(this.isDetail) {
            this.orderNum();
        }
        /* this.$nextTick(() => {
            // 选中所有行
            // this.$refs.productTable && this.$refs.productTable.toggleAllSelection();

            // 选中所有行
            // this.toggleRowSelection(this.brand.map(a => a), true);
        }) */
    }
}
</script>

<style lang="less">
.product-table-list {
    .el-table {
        th {
            background-color: #F7F8FA;
            border-bottom: none;
            color: #181C25;
            font-size: 12px;
            padding: 7px 0;
        }

        td {
            // border-bottom: none;
        }

        .row-giveaway-bg {
            .el-checkbox {
                display: none; // 隐藏复选框
            }
            .el-table-column--selection {
                position: relative;     
                &::before {
                    border-radius: 2px;
                    content: "赠品";
                    color: #999;
                    padding: 3px 6px;
                    background-color: #F2F4FB;
                    font-weight: bold;
                    position: absolute;
                    left: 16px; // 根据需要调整位置
                    top: 50%;
                    transform: translateY(-50%);
                }
            }
        }

        .product-r-wrap {
            .product-r-item {
                display: flex;

                .product-r-detail {
                    margin-left: 12px;

                    .product-r-label {
                        color: #181C25;
                    }

                    .product-r-unit {
                        margin: 4px 0;
                        color: #91959E;
                    }
                }
            }
            .product-r-policy {
                margin-top: 4px;
                font-size: 12px;
                &:first-child {
                    margin-top: 16px;
                }
                .policy-name {
                    display: inline-block;
                    height: 20px;
                    padding: 0 4px;
                    border-radius: 2px;
                    border: 1px solid #ffbda3;
                    background: #fff5f0;
                    color: #FF522A;
                    line-height: 20px;
                }
                .policy-content {
                    margin-left: 8px;
                    .icon {
                        transform: scale(0.8); 
                    }
                }
            }
        }

        .table-tb-num {
            display: flex;
            color: #FF522A;
            font-size: 16px;
            font-weight: 700;

            .unit {
                font-size: 12px;
                font-weight: 400;
                margin-left: 4px;
                margin-top: 2px;
            }
        }
    }

    .order-num {
		border-top: none !important;
    }
}
</style>
