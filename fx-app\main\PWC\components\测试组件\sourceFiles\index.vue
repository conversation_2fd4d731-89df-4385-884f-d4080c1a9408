<template>
  <fx-row>
    <fx-col :span="8" v-for="(o, index) in 2" :key="o" :offset="index > 0 ? 2 : 0">
      <fx-card :body-style="{ padding: '0px' }">
        <img :src="pictureUrl"  alt="preview" @click="goToLink" >
        <div style="padding: 14px;">
          <span>好看的篮球</span>
          <div class="bottom clearfix">
            <time class="time">{{ currentDate }}</time>
            <fx-button type="text" class="button">操作按钮</fx-button>
          </div>
        </div>
      </fx-card>
    </fx-col>
  </fx-row>
</template>
 
 
<script> 
// import FxUI from 'fxui-mobile';
export default {
  data() {
    return {
      url: "https://img.fxiaoke.com/image/i/N_202507_10_f4b21ee4c04b4be2aee14c9b599be9eb/0*0/jpg",
      file : {
        filename: "篮球.png",
        path : "N_202507_10_f4b21ee4c04b4be2aee14c9b599be9eb.png",
        ext: "png"
      }
    };
 
  },
  created() { 
    },
  methods: {
    goToLink(){
      var url1 = "https://www.fxiaoke.com/XV/Cross/Portal?fs_out_appid=FSAID_11490c84#/portal/depend/dht-api-ShopMall";
      window.open(url1, '_blank') // 新标签页打开
    },
    // 图片地址
    pictureUrl() { 
      cconsole.log( `${this.getImgHost()}/image/o/N_202507_10_f4b21ee4c04b4be2aee14c9b599be9eb/350*350/jpg/FSAID_11490c84`)
       return 'https://a9.fspage.com/FSR/weex/avatar/object_list/images/list_default_icon.png';
      // return `${this.getImgHost()}/image/o/N_202507_10_f4b21ee4c04b4be2aee14c9b599be9eb/350*350/jpg/FSAID_11490c84`;
    },
  }
}
</script>
  
<style>
  .time {
    font-size: 13px;
    color: #999;
  }
  
  .bottom {
    margin-top: 13px;
    line-height: 12px;
  }

  .button {
    padding: 0;
    float: right;
  }

  .image {
    width: 100%;
    display: block;
  }

  .clearfix:before,
  .clearfix:after {
      display: table;
      content: "";
  }
  
  .clearfix:after {
      clear: both
  }
</style>

